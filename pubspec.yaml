name: focusbro
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.2.3 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  audioplayers: ^6.4.0
  shared_preferences: ^2.2.2
  intl: ^0.20.2
  fl_chart: ^1.0.0
  path_provider: ^2.1.1
  pdf: ^3.10.7
  share_plus: ^11.0.0
  flutter_local_notifications: ^19.2.1
  # window_manager: ^0.4.3
  provider: ^6.1.1
  image_picker: ^1.0.5
  file_picker: ^10.1.9
  confetti: ^0.8.0
  syncfusion_flutter_pdfviewer: ^29.2.4
  syncfusion_flutter_pdf: ^29.2.4
  permission_handler: ^12.0.0+1
  sqflite: ^2.3.0
  path: ^1.8.3
  timezone: ^0.10.0
  id3: ^1.0.2
  mime: ^1.0.4

  # Focus mode features
  screen_brightness: ^0.2.2+1
  url_launcher: ^6.2.1
  webview_flutter: ^4.4.2
  csv: ^6.0.0

  # Security features
  local_auth: ^2.1.6
  crypto: ^3.0.3
  device_info_plus: ^11.4.0
  package_info_plus: ^8.0.2

  # Internationalization
  flutter_localizations:
    sdk: flutter

  # Voice commands and TTS
  speech_to_text: ^7.0.0
  flutter_tts: ^3.8.5
  # record: ^5.0.4 (keeping disabled for now)
  # just_audio: ^0.9.36 (keeping disabled for now)

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.3
  sqflite_common_ffi: ^2.3.0

  # Integration testing
  integration_test:
    sdk: flutter
  flutter_driver:
    sdk: flutter

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/focusBroLogo.png"
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/focusBroLogoNew.png"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/focusBroLogoName.png
    - assets/focusBroLogoWhite.png
    - assets/sounds/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Poppins
      fonts:
        - asset: fonts/Poppins-Regular.ttf
        - asset: fonts/Poppins-Medium.ttf
          weight: 500
        - asset: fonts/Poppins-SemiBold.ttf
          weight: 600
        - asset: fonts/Poppins-Bold.ttf
          weight: 700
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
