import 'package:flutter/material.dart';

class TermsOfServiceScreen extends StatelessWidget {
  const TermsOfServiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Terms of Service'),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'FocusBro Terms of Service',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Last updated: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 24),

            // Introduction
            _buildSection(
              context,
              'Agreement to Terms',
              'By downloading, installing, or using the FocusBro mobile application, you agree to be bound by these Terms of Service. If you do not agree to these terms, please do not use our app.',
            ),

            // Description of Service
            _buildSection(
              context,
              'Description of Service',
              '''FocusBro is a productivity application that provides:

• Pomodoro timer functionality
• Task and agenda management
• Note-taking capabilities
• Focus mode features
• Music and ambient sound integration
• Analytics and progress tracking''',
            ),

            // User Responsibilities
            _buildSection(
              context,
              'User Responsibilities',
              '''As a user of FocusBro, you agree to:

• Use the app for lawful purposes only
• Not attempt to reverse engineer or modify the app
• Not share your account credentials with others
• Respect intellectual property rights
• Provide accurate information when required''',
            ),

            // Acceptable Use
            _buildSection(
              context,
              'Acceptable Use Policy',
              '''You may not use FocusBro to:

• Violate any applicable laws or regulations
• Infringe on others' intellectual property rights
• Transmit harmful or malicious content
• Attempt to gain unauthorized access to our systems
• Use the app for commercial purposes without permission''',
            ),

            // Intellectual Property
            _buildSection(
              context,
              'Intellectual Property Rights',
              '''• FocusBro and all related content are owned by us or our licensors
• You are granted a limited, non-exclusive license to use the app
• You may not copy, modify, or distribute the app without permission
• All trademarks and logos are the property of their respective owners''',
            ),

            // Privacy and Data
            _buildSection(
              context,
              'Privacy and Data Protection',
              '''• Your privacy is important to us
• Please review our Privacy Policy for details on data collection
• You retain ownership of your personal data
• We implement security measures to protect your information''',
            ),

            // Disclaimers
            _buildSection(
              context,
              'Disclaimers and Limitations',
              '''• FocusBro is provided "as is" without warranties
• We do not guarantee uninterrupted or error-free service
• We are not liable for any indirect or consequential damages
• Your use of the app is at your own risk''',
            ),

            // Updates and Changes
            _buildSection(
              context,
              'App Updates and Changes',
              '''• We may update the app to improve functionality
• We may modify these terms with notice to users
• Continued use after changes constitutes acceptance
• We reserve the right to discontinue features''',
            ),

            // Termination
            _buildSection(
              context,
              'Termination',
              '''• You may stop using FocusBro at any time
• We may terminate access for violations of these terms
• Upon termination, your license to use the app ends
• Data deletion procedures are outlined in our Privacy Policy''',
            ),

            // Contact Information
            _buildSection(
              context,
              'Contact Information',
              '''For questions about these Terms of Service:

• Email: <EMAIL>
• Through the app's Help & Support section
• We will respond to inquiries within 48 hours''',
            ),

            const SizedBox(height: 32),
            
            // Footer
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.gavel,
                    color: theme.colorScheme.primary,
                    size: 32,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Fair and Transparent Terms',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'These terms are designed to protect both users and developers while ensuring a great experience for everyone.',
                    style: theme.textTheme.bodySmall,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, String content) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: theme.textTheme.bodyMedium?.copyWith(
              height: 1.6,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }
}
