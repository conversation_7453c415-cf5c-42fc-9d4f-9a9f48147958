import 'package:flutter/material.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Privacy Policy'),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'FocusBro Privacy Policy',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Last updated: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 24),

            // Introduction
            _buildSection(
              context,
              'Introduction',
              'FocusBro ("we", "our", or "us") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, and safeguard your information when you use our mobile application.',
            ),

            // Information We Collect
            _buildSection(
              context,
              'Information We Collect',
              '''We collect the following types of information:

• Personal Information: Name, email address (if provided)
• Usage Data: App usage statistics, session data, focus time
• Device Information: Device type, operating system version
• Local Data: Tasks, notes, timer presets, and app settings stored locally on your device''',
            ),

            // How We Use Your Information
            _buildSection(
              context,
              'How We Use Your Information',
              '''We use your information to:

• Provide and maintain the FocusBro service
• Improve app functionality and user experience
• Generate usage analytics and statistics
• Send important updates and notifications
• Provide customer support when requested''',
            ),

            // Data Storage and Security
            _buildSection(
              context,
              'Data Storage and Security',
              '''• Local Storage: Most of your data (tasks, notes, settings) is stored locally on your device
• Cloud Backup: Optional cloud backup feature (if enabled) stores encrypted data
• Security: We implement appropriate security measures to protect your information
• No Third-Party Sharing: We do not sell or share your personal data with third parties''',
            ),

            // Your Rights
            _buildSection(
              context,
              'Your Rights',
              '''You have the right to:

• Access your personal data
• Correct inaccurate information
• Delete your data (through app settings)
• Export your data (backup feature)
• Opt-out of analytics (through app settings)''',
            ),

            // Analytics and Tracking
            _buildSection(
              context,
              'Analytics and Tracking',
              '''• We may collect anonymous usage statistics to improve the app
• You can disable analytics in the app settings
• We do not track your personal activities outside the app
• No advertising or marketing tracking is performed''',
            ),

            // Children's Privacy
            _buildSection(
              context,
              'Children\'s Privacy',
              'FocusBro is not intended for children under 13. We do not knowingly collect personal information from children under 13.',
            ),

            // Changes to Privacy Policy
            _buildSection(
              context,
              'Changes to This Privacy Policy',
              'We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy in the app.',
            ),

            // Contact Information
            _buildSection(
              context,
              'Contact Us',
              '''If you have any questions about this Privacy Policy, please contact us:

• Email: <EMAIL>
• Through the app's Help & Support section''',
            ),

            const SizedBox(height: 32),
            
            // Footer
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.security,
                    color: theme.colorScheme.primary,
                    size: 32,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Your Privacy Matters',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'We are committed to protecting your privacy and being transparent about our data practices.',
                    style: theme.textTheme.bodySmall,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, String content) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: theme.textTheme.bodyMedium?.copyWith(
              height: 1.6,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }
}
