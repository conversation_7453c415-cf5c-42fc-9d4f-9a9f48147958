import 'dart:io';

/// Voice Note model for storing voice recording data
class VoiceNote {
  final String id;
  final String title;
  final String? transcription;
  final String audioFilePath;
  final Duration duration;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String category;
  final bool isFavorite;
  final List<String> tags;

  const VoiceNote({
    required this.id,
    required this.title,
    this.transcription,
    required this.audioFilePath,
    required this.duration,
    required this.createdAt,
    required this.updatedAt,
    this.category = 'Voice Notes',
    this.isFavorite = false,
    this.tags = const [],
  });

  /// Create a copy of this voice note with updated fields
  VoiceNote copyWith({
    String? id,
    String? title,
    String? transcription,
    String? audioFilePath,
    Duration? duration,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? category,
    bool? isFavorite,
    List<String>? tags,
  }) {
    return VoiceNote(
      id: id ?? this.id,
      title: title ?? this.title,
      transcription: transcription ?? this.transcription,
      audioFilePath: audioFilePath ?? this.audioFilePath,
      duration: duration ?? this.duration,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      category: category ?? this.category,
      isFavorite: isFavorite ?? this.isFavorite,
      tags: tags ?? this.tags,
    );
  }

  /// Convert voice note to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'transcription': transcription,
      'audioFilePath': audioFilePath,
      'duration': duration.inMilliseconds,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'category': category,
      'isFavorite': isFavorite ? 1 : 0,
      'tags': tags.join(','),
    };
  }

  /// Create voice note from JSON
  factory VoiceNote.fromJson(Map<String, dynamic> json) {
    return VoiceNote(
      id: json['id'] as String,
      title: json['title'] as String,
      transcription: json['transcription'] as String?,
      audioFilePath: json['audioFilePath'] as String,
      duration: Duration(milliseconds: json['duration'] as int),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      category: json['category'] as String? ?? 'Voice Notes',
      isFavorite: (json['isFavorite'] as int) == 1,
      tags: json['tags'] != null && (json['tags'] as String).isNotEmpty
          ? (json['tags'] as String).split(',')
          : [],
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'transcription': transcription,
      'audio_file_path': audioFilePath,
      'duration_ms': duration.inMilliseconds,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'category': category,
      'is_favorite': isFavorite ? 1 : 0,
      'tags': tags.join(','),
    };
  }

  /// Create voice note from database map
  factory VoiceNote.fromMap(Map<String, dynamic> map) {
    return VoiceNote(
      id: map['id'] as String,
      title: map['title'] as String,
      transcription: map['transcription'] as String?,
      audioFilePath: map['audio_file_path'] as String,
      duration: Duration(milliseconds: map['duration_ms'] as int),
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
      category: map['category'] as String? ?? 'Voice Notes',
      isFavorite: (map['is_favorite'] as int) == 1,
      tags: map['tags'] != null && (map['tags'] as String).isNotEmpty
          ? (map['tags'] as String).split(',')
          : [],
    );
  }

  /// Get formatted duration string
  String get formattedDuration {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// Get file size if available
  Future<String> getFileSize() async {
    try {
      final file = File(audioFilePath);
      if (await file.exists()) {
        final bytes = await file.length();
        if (bytes < 1024) {
          return '${bytes}B';
        } else if (bytes < 1024 * 1024) {
          return '${(bytes / 1024).toStringAsFixed(1)}KB';
        } else {
          return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
        }
      }
      return 'Unknown';
    } catch (e) {
      return 'Unknown';
    }
  }

  @override
  String toString() {
    return 'VoiceNote{id: $id, title: $title, duration: $formattedDuration, category: $category}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VoiceNote && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
