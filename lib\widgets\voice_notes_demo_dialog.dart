import 'package:flutter/material.dart';
import '../widgets/voice_note_recorder.dart';
import '../widgets/voice_note_player.dart';
import '../models/voice_note.dart';

/// Demo dialog to showcase voice notes functionality
class VoiceNotesDemoDialog extends StatefulWidget {
  final Function(String transcription)? onTranscriptionComplete;

  const VoiceNotesDemoDialog({
    super.key,
    this.onTranscriptionComplete,
  });

  @override
  State<VoiceNotesDemoDialog> createState() => _VoiceNotesDemoDialogState();
}

class _VoiceNotesDemoDialogState extends State<VoiceNotesDemoDialog>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<VoiceNote> _savedVoiceNotes = [];
  String _lastTranscription = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.95,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Header
            _buildHeader(theme, colorScheme),
            const SizedBox(height: 20),

            // Tab Bar
            _buildTabBar(theme, colorScheme),
            const SizedBox(height: 20),

            // Tab Content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildRecorderTab(),
                  _buildLibraryTab(),
                ],
              ),
            ),

            // Action Buttons
            _buildActionButtons(theme, colorScheme),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, ColorScheme colorScheme) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            Icons.mic,
            color: colorScheme.onPrimaryContainer,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Voice Notes',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Record, transcribe, and manage voice notes',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
          tooltip: 'Close',
        ),
      ],
    );
  }

  Widget _buildTabBar(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: colorScheme.primary,
          borderRadius: BorderRadius.circular(8),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: colorScheme.onSurfaceVariant,
        dividerColor: Colors.transparent,
        tabs: const [
          Tab(
            icon: Icon(Icons.mic),
            text: 'Record',
          ),
          Tab(
            icon: Icon(Icons.library_music),
            text: 'Library',
          ),
        ],
      ),
    );
  }

  Widget _buildRecorderTab() {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Voice Note Recorder
          VoiceNoteRecorder(
            onVoiceNoteSaved: _onVoiceNoteSaved,
            onTranscriptionComplete: _onTranscriptionComplete,
            enableTranscription: true,
            showSaveDialog: false,
          ),

          // Last Transcription Display
          if (_lastTranscription.isNotEmpty) ...[
            const SizedBox(height: 20),
            _buildTranscriptionPreview(),
          ],

          // Demo Features Info
          const SizedBox(height: 20),
          _buildDemoInfo(),
        ],
      ),
    );
  }

  Widget _buildLibraryTab() {
    if (_savedVoiceNotes.isEmpty) {
      return _buildEmptyLibrary();
    }

    return ListView.builder(
      itemCount: _savedVoiceNotes.length,
      itemBuilder: (context, index) {
        final voiceNote = _savedVoiceNotes[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: VoiceNotePlayer(
            voiceNote: voiceNote,
            onDelete: _deleteVoiceNote,
            onEdit: _editVoiceNote,
            onShare: _shareVoiceNote,
            compact: true,
            showControls: true,
            showTranscription: true,
          ),
        );
      },
    );
  }

  Widget _buildTranscriptionPreview() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.transcribe,
                color: colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Latest Transcription',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            _lastTranscription,
            style: theme.textTheme.bodyMedium,
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton.icon(
                onPressed: _useTranscription,
                icon: const Icon(Icons.check, size: 16),
                label: const Text('Use in Note'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDemoInfo() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Voice Notes Features',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildFeatureItem(
            icon: Icons.mic,
            title: 'High-Quality Recording',
            description: 'Record audio with configurable quality settings',
          ),
          _buildFeatureItem(
            icon: Icons.transcribe,
            title: 'Speech-to-Text',
            description: 'Convert voice recordings to text automatically',
          ),
          _buildFeatureItem(
            icon: Icons.play_circle,
            title: 'Advanced Playback',
            description: 'Play with speed control, seek, and progress tracking',
          ),
          _buildFeatureItem(
            icon: Icons.folder,
            title: 'Voice Library',
            description: 'Organize and manage all your voice notes',
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyLibrary() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.library_music_outlined,
            size: 64,
            color: colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No Voice Notes Yet',
            style: theme.textTheme.titleMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Record your first voice note to get started',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          FilledButton.icon(
            onPressed: () => _tabController.animateTo(0),
            icon: const Icon(Icons.mic),
            label: const Text('Start Recording'),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ThemeData theme, ColorScheme colorScheme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (_lastTranscription.isNotEmpty)
          FilledButton.icon(
            onPressed: _useTranscription,
            icon: const Icon(Icons.check),
            label: const Text('Use Transcription'),
          ),
        const SizedBox(width: 12),
        OutlinedButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  // Event Handlers
  void _onVoiceNoteSaved(VoiceNote voiceNote) {
    setState(() {
      _savedVoiceNotes.insert(0, voiceNote);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Voice note "${voiceNote.title}" saved!'),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );

    // Switch to library tab to show the saved note
    _tabController.animateTo(1);
  }

  void _onTranscriptionComplete(String transcription) {
    setState(() {
      _lastTranscription = transcription;
    });
  }

  void _useTranscription() {
    widget.onTranscriptionComplete?.call(_lastTranscription);
    Navigator.of(context).pop();
  }

  void _deleteVoiceNote(VoiceNote voiceNote) {
    setState(() {
      _savedVoiceNotes.removeWhere((note) => note.id == voiceNote.id);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Voice note "${voiceNote.title}" deleted'),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _editVoiceNote(VoiceNote voiceNote) {
    // Placeholder for edit functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Edit functionality coming soon!'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _shareVoiceNote(String audioPath) {
    // Placeholder for share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality coming soon!'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
